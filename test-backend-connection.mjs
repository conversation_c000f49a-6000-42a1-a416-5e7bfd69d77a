#!/usr/bin/env node

// Test script to check if backend is running
import fetch from 'node-fetch';

const API_BASE = 'http://localhost:3001';

async function testBackend() {
    console.log('🔍 Testing MEV Arbitrage Bot Backend Connection...');
    console.log('================================================\n');
    
    try {
        console.log(`📡 Testing health endpoint: ${API_BASE}/health`);
        const response = await fetch(`${API_BASE}/health`);
        
        if (response.ok) {
            const data = await response.json();
            console.log('✅ Backend is running!');
            console.log('📊 Health Status:', JSON.stringify(data, null, 2));
            
            // Test other endpoints
            console.log('\n🔍 Testing other endpoints...');
            
            const endpoints = [
                '/api/opportunities',
                '/api/trades', 
                '/api/tokens',
                '/api/analytics/performance',
                '/api/system/health'
            ];
            
            for (const endpoint of endpoints) {
                try {
                    const res = await fetch(`${API_BASE}${endpoint}`);
                    const data = await res.json();
                    console.log(`✅ ${endpoint}: ${data.success ? 'OK' : 'ERROR'} (${data.count || 'N/A'} items)`);
                } catch (err) {
                    console.log(`❌ ${endpoint}: ERROR - ${err.message}`);
                }
            }
            
            console.log('\n🎉 All tests completed!');
            console.log('🌐 You can now open index.html in your browser');
            console.log(`🔗 Direct link: file://${process.cwd()}/index.html`);
            
        } else {
            console.log(`❌ Backend responded with status: ${response.status}`);
        }
        
    } catch (error) {
        console.log('❌ Backend is not running or not accessible');
        console.log('💡 Error:', error.message);
        console.log('\n🔧 Troubleshooting:');
        console.log('1. Make sure the backend is running: node working-backend.mjs');
        console.log('2. Check if port 8080 is available');
        console.log('3. Verify no firewall is blocking the connection');
    }
}

testBackend();
